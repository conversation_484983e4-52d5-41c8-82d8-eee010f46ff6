/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme (Default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --accent-primary: #3498db;
    --accent-secondary: #2980b9;
    --highlight-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --border-color: #dee2e6;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size: 16px;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #2c3e50;
    --bg-secondary: #34495e;
    --bg-tertiary: #3d566e;
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
    --accent-primary: #3498db;
    --accent-secondary: #2980b9;
    --highlight-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --border-color: #4a5f7a;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Blue Theme */
[data-theme="blue"] {
    --bg-primary: #f0f8ff;
    --bg-secondary: #e6f3ff;
    --bg-tertiary: #cce7ff;
    --text-primary: #1e3a8a;
    --text-secondary: #3b82f6;
    --accent-primary: #2563eb;
    --accent-secondary: #1d4ed8;
    --highlight-color: #dc2626;
    --success-color: #059669;
    --warning-color: #d97706;
    --border-color: #93c5fd;
    --shadow-color: rgba(37, 99, 235, 0.1);
}

/* Green Theme */
[data-theme="green"] {
    --bg-primary: #f0fdf4;
    --bg-secondary: #dcfce7;
    --bg-tertiary: #bbf7d0;
    --text-primary: #14532d;
    --text-secondary: #16a34a;
    --accent-primary: #22c55e;
    --accent-secondary: #16a34a;
    --highlight-color: #dc2626;
    --success-color: #059669;
    --warning-color: #d97706;
    --border-color: #86efac;
    --shadow-color: rgba(34, 197, 94, 0.1);
}

/* Purple Theme */
[data-theme="purple"] {
    --bg-primary: #faf5ff;
    --bg-secondary: #f3e8ff;
    --bg-tertiary: #e9d5ff;
    --text-primary: #581c87;
    --text-secondary: #7c3aed;
    --accent-primary: #8b5cf6;
    --accent-secondary: #7c3aed;
    --highlight-color: #dc2626;
    --success-color: #059669;
    --warning-color: #d97706;
    --border-color: #c4b5fd;
    --shadow-color: rgba(139, 92, 246, 0.1);
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    transition: all 0.3s ease;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.title-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.app-icon {
    font-size: 2.5em;
    color: var(--accent-primary);
    text-shadow: 2px 2px 4px var(--shadow-color);
}

.title-text h1 {
    margin: 0;
    color: var(--text-primary);
    font-size: 2em;
}

.title-text p {
    margin: 5px 0 0 0;
    color: var(--text-secondary);
    font-size: 0.9em;
}

.header-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.theme-controls, .font-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.theme-controls label, .font-controls label {
    color: var(--text-secondary);
    font-weight: 500;
}

.theme-controls select, .font-controls select {
    padding: 4px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9em;
}

.font-controls input[type="range"] {
    width: 60px;
}

#font-size-display {
    color: var(--text-secondary);
    font-size: 0.8em;
    min-width: 35px;
}

/* Main Content Layout */
.main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.main-content.compact {
    flex-direction: column;
}

.main-content.compact .sidebar {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
}

.main-content.compact main {
    width: 100%;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    flex-shrink: 0;
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
    border: 1px solid var(--border-color);
    padding: 15px;
    height: fit-content;
    max-height: 80vh;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.left-sidebar {
    width: 250px;
}

.right-sidebar {
    width: 300px;
}

.filter-section h3, .summary-section h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #2c3e50;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-actions button {
    flex: 1;
    padding: 8px;
    font-size: 14px;
}

.cipher-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 5px; /* Add some space for scrollbar */
}

/* Scrollbar styling for cipher filters */
.cipher-filters::-webkit-scrollbar {
    width: 8px;
}

.cipher-filters::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.cipher-filters::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.cipher-filters::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.cipher-filter-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.cipher-filter-item:hover {
    background-color: #f0f7ff;
}

.cipher-filter-item input {
    margin-right: 10px;
    cursor: pointer;
}

.cipher-filter-item label {
    cursor: pointer;
    font-size: 14px;
    flex: 1;
}

/* Category Headers for Filters */
.cipher-category-header {
    font-weight: bold;
    font-size: 15px;
    color: #2c3e50;
    margin-top: 15px;
    margin-bottom: 5px;
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 3px solid #3498db;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    user-select: none;
}

.cipher-category-header:hover {
    background-color: #d4e6f7;
}

.cipher-category-header::after {
    content: '▼';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    transition: transform 0.2s;
}

.cipher-category-header.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.cipher-category-items {
    display: block;
    margin-bottom: 10px;
}

/* Summary Styles */
#summary-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 70vh;
    overflow-y: auto;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border-left: 3px solid #3498db;
    font-size: 14px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.summary-item:hover {
    background-color: #f0f7ff;
}

.summary-name {
    font-weight: 500;
}

.summary-values {
    font-weight: bold;
    color: #2c3e50;
}

/* Summary Category Headers */
.summary-category-header {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    margin-top: 15px;
    margin-bottom: 5px;
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 3px solid #3498db;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    user-select: none;
}

.summary-category-header:hover {
    background-color: #d4e6f7;
}

.summary-category-header::after {
    content: '▼';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    transition: transform 0.2s;
}

.summary-category-header.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.summary-category-items {
    display: block;
    margin-bottom: 10px;
}

/* Main Content Styles */
main {
    flex: 1;
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
    border: 1px solid var(--border-color);
    padding: 20px;
    transition: all 0.3s ease;
}

.input-section {
    margin-bottom: 20px;
}

.input-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.text-controls {
    display: flex;
    gap: 8px;
}

.text-controls button {
    padding: 8px 12px;
    font-size: 1.2em;
    min-width: 40px;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.text-controls button:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
}

.text-controls button:disabled {
    background-color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

.target-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.target-controls label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9em;
}

.target-controls input[type="number"] {
    width: 120px;
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9em;
}

.target-controls button {
    padding: 6px 12px;
    font-size: 0.8em;
    background-color: var(--warning-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.target-controls button:hover {
    opacity: 0.9;
}

textarea {
    width: 100%;
    height: 100px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: var(--font-size);
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    resize: vertical;
    margin-bottom: 10px;
    transition: all 0.2s ease;
    -webkit-font-smoothing: antialiased;
}

textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

.results-section h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.empty-result {
    color: var(--text-secondary);
    font-style: italic;
}

/* Target Matches Styles */
.target-matches {
    background-color: var(--highlight-color);
    color: white;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    animation: slideDown 0.3s ease;
}

.target-matches h3 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
}

.target-matches-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.target-match-item {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
}

/* Results Header */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.view-controls {
    display: flex;
    gap: 8px;
}

.view-controls button {
    padding: 6px 12px;
    font-size: 0.9em;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-controls button:hover {
    background-color: var(--accent-secondary);
}

.view-controls button.active {
    background-color: var(--success-color);
}

/* Comparison View */
.comparison-view {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid var(--border-color);
}

.comparison-side {
    flex: 1;
    background-color: var(--bg-secondary);
    padding: 15px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.comparison-side h3 {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 1.1em;
}

.comparison-side textarea {
    height: 60px;
    margin-bottom: 15px;
}

/* Highlighted Results */
.result-item.target-match {
    border: 2px solid var(--highlight-color) !important;
    background-color: rgba(231, 76, 60, 0.1) !important;
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Results Display Styles - 2 Column Grid Layout */
#results-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
    width: 100%;
    overflow: hidden; /* Prevent grid from expanding beyond container */
}

.cipher-result {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
    margin-bottom: 0; /* Remove margin since grid handles spacing */
    min-width: 0; /* Allow flex items to shrink below their content size */
    overflow: hidden; /* Prevent content from overflowing the container */
}

/* Remove category headers - we'll show categories as tags instead */
.result-category-header {
    display: none;
}

.result-category-container {
    display: contents; /* This makes the container transparent to the grid */
}

.cipher-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.cipher-name {
    font-weight: bold;
    font-size: 16px;
    flex: 1;
}

.cipher-category-tag {
    background-color: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-left: 10px;
    margin-right: 10px;
}

.cipher-value {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    min-width: 60px;
    text-align: right;
}

.cipher-description {
    color: #666;
    font-size: 12px;
    margin-bottom: 10px;
    line-height: 1.3;
    overflow-x: auto;
    white-space: nowrap;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 6px 8px;
    background-color: #fafafa;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
}

.cipher-description::-webkit-scrollbar {
    height: 6px;
}

.cipher-description::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.cipher-description::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.cipher-description::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.word-breakdown {
    margin-top: 10px;
}

.word-breakdown h4 {
    font-size: 14px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.word-item {
    background-color: #e8f4f8;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
}

.word-name {
    font-weight: bold;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.word-value {
    color: #3498db;
    font-weight: bold;
}

.letter-breakdown {
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    #results-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .cipher-result {
        padding: 12px;
    }

    .cipher-name {
        font-size: 14px;
    }

    .cipher-value {
        font-size: 16px;
    }
}

.word-breakdown h4 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.word-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-start;
}

.word-item {
    background-color: #e8f4fc;
    padding: 12px 15px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding-bottom: 5px;
}

.word-text {
    font-weight: 500;
}

.word-value {
    font-weight: bold;
    color: #3498db;
}

.letter-breakdown {
    font-family: monospace;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

/* Footer Styles */
footer {
    text-align: center;
    padding: 10px;
    color: #666;
    font-size: 14px;
}

/* Grid Layout for Results */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Hide Calculate Button when Auto-Calculate is enabled */
.hidden {
    display: none;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .main-content {
        flex-wrap: wrap;
    }

    main {
        order: 1;
        width: 100%;
    }

    .left-sidebar {
        order: 2;
        width: calc(50% - 10px);
    }

    .right-sidebar {
        order: 3;
        width: calc(50% - 10px);
    }
}

@media (max-width: 900px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: none;
    }

    .cipher-filters, #summary-container {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header, main {
        padding: 15px;
    }

    .cipher-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .word-list {
        flex-direction: column;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }
}

/* Add this to your existing CSS */
.search-box {
    margin-bottom: 15px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Navigation Styles */
nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

nav li, .main-nav li {
    margin: 0 15px;
}

nav a, .main-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

nav a:hover, nav a.active, .main-nav a:hover, .main-nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group textarea {
    min-height: 150px;
}

.form-submit {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.form-submit:hover {
    background-color: #2980b9;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 4px 20px var(--shadow-color);
    border: 1px solid var(--border-color);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5em;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
}

.color-picker-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.color-picker-group label {
    color: var(--text-primary);
    font-weight: 500;
}

.color-picker-group input[type="color"] {
    width: 50px;
    height: 30px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.saved-words-list {
    max-height: 300px;
    overflow-y: auto;
}

.saved-word-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
    background-color: var(--bg-secondary);
}

.saved-word-text {
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
}

.saved-word-text:hover {
    color: var(--accent-primary);
}

.saved-word-actions {
    display: flex;
    gap: 5px;
}

.saved-word-actions button {
    padding: 4px 8px;
    font-size: 0.8em;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.delete-word-btn {
    background-color: var(--highlight-color);
    color: white;
}

.use-word-btn {
    background-color: var(--success-color);
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Comparison Results Styles */
.comparison-category {
    margin-bottom: 15px;
}

.comparison-category h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 0.9em;
    padding-bottom: 4px;
    border-bottom: 1px solid var(--border-color);
}

.comparison-results {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.comparison-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background-color: var(--bg-tertiary);
    border-radius: 3px;
    font-size: 0.85em;
}

.comparison-result-item.target-match {
    background-color: rgba(231, 76, 60, 0.2);
    border: 1px solid var(--highlight-color);
}

.comparison-result-item .cipher-name {
    flex: 1;
    color: var(--text-primary);
}

.comparison-result-item .cipher-value {
    font-weight: bold;
    color: var(--accent-primary);
    min-width: 40px;
    text-align: right;
}

/* Notification Animation */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Update existing result styles for better theming */
.cipher-result {
    background-color: var(--bg-primary) !important;
    border: 1px solid var(--border-color) !important;
    border-left: 4px solid var(--accent-primary) !important;
    color: var(--text-primary) !important;
}

.cipher-name {
    color: var(--text-primary) !important;
}

.cipher-value {
    color: var(--accent-primary) !important;
}

.cipher-category-tag {
    background-color: var(--accent-primary) !important;
}

.cipher-description {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-secondary) !important;
}

.word-item {
    background-color: var(--bg-secondary) !important;
    border: 1px solid var(--border-color) !important;
}

.word-value {
    color: var(--accent-primary) !important;
}

.letter-breakdown {
    color: var(--text-secondary) !important;
}

/* Cipher Management Modal Styles */
.large-modal {
    max-width: 700px;
    width: 95%;
}

.cipher-management-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.2s ease;
}

.tab-btn.active {
    color: var(--accent-primary);
    border-bottom-color: var(--accent-primary);
}

.tab-btn:hover {
    color: var(--text-primary);
}

.tab-content {
    display: block;
}

.tab-content.hidden {
    display: none;
}

.rename-systems-list {
    max-height: 400px;
    overflow-y: auto;
}

.rename-system-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
    background-color: var(--bg-secondary);
}

.system-name-input {
    background: none;
    border: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1em;
    padding: 4px 8px;
    border-radius: 3px;
    transition: all 0.2s ease;
    flex: 1;
}

.system-name-input:hover {
    background-color: var(--bg-tertiary);
}

.system-name-input:focus {
    outline: none;
    background-color: var(--bg-primary);
    border: 1px solid var(--accent-primary);
}

.system-category {
    background-color: var(--accent-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-left: 10px;
}

.custom-values-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    margin-top: 10px;
}

.custom-value-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.custom-value-item label {
    font-weight: bold;
    color: var(--text-primary);
    font-size: 0.9em;
}

.custom-value-item input {
    width: 50px;
    padding: 4px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1em;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}
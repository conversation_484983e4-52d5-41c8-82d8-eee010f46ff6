/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #2c3e50;
    color: white;
    border-radius: 5px;
}

header h1 {
    margin-bottom: 10px;
}

/* Main Content Layout */
.main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    flex-shrink: 0;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    height: fit-content;
    max-height: 80vh;
    overflow-y: auto;
}

.left-sidebar {
    width: 250px;
}

.right-sidebar {
    width: 300px;
}

.filter-section h3, .summary-section h3 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    color: #2c3e50;
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.filter-actions button {
    flex: 1;
    padding: 8px;
    font-size: 14px;
}

.cipher-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 5px; /* Add some space for scrollbar */
}

/* Scrollbar styling for cipher filters */
.cipher-filters::-webkit-scrollbar {
    width: 8px;
}

.cipher-filters::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.cipher-filters::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.cipher-filters::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.cipher-filter-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.cipher-filter-item:hover {
    background-color: #f0f7ff;
}

.cipher-filter-item input {
    margin-right: 10px;
    cursor: pointer;
}

.cipher-filter-item label {
    cursor: pointer;
    font-size: 14px;
    flex: 1;
}

/* Category Headers for Filters */
.cipher-category-header {
    font-weight: bold;
    font-size: 15px;
    color: #2c3e50;
    margin-top: 15px;
    margin-bottom: 5px;
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 3px solid #3498db;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    user-select: none;
}

.cipher-category-header:hover {
    background-color: #d4e6f7;
}

.cipher-category-header::after {
    content: '▼';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    transition: transform 0.2s;
}

.cipher-category-header.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.cipher-category-items {
    display: block;
    margin-bottom: 10px;
}

/* Summary Styles */
#summary-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 70vh;
    overflow-y: auto;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    border-radius: 4px;
    background-color: #f9f9f9;
    border-left: 3px solid #3498db;
    font-size: 14px;
    transition: background-color 0.2s;
    margin-left: 10px;
}

.summary-item:hover {
    background-color: #f0f7ff;
}

.summary-name {
    font-weight: 500;
}

.summary-values {
    font-weight: bold;
    color: #2c3e50;
}

/* Summary Category Headers */
.summary-category-header {
    font-weight: bold;
    font-size: 14px;
    color: #2c3e50;
    margin-top: 15px;
    margin-bottom: 5px;
    padding: 5px 8px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 3px solid #3498db;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    user-select: none;
}

.summary-category-header:hover {
    background-color: #d4e6f7;
}

.summary-category-header::after {
    content: '▼';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    transition: transform 0.2s;
}

.summary-category-header.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.summary-category-items {
    display: block;
    margin-bottom: 10px;
}

/* Main Content Styles */
main {
    flex: 1;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.input-section {
    margin-bottom: 20px;
}

textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    resize: vertical;
    margin-bottom: 10px;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

.results-section h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.empty-result {
    color: #999;
    font-style: italic;
}

/* Results Display Styles - 2 Column Grid Layout */
#results-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
    width: 100%;
    overflow: hidden; /* Prevent grid from expanding beyond container */
}

.cipher-result {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 4px solid #3498db;
    margin-bottom: 0; /* Remove margin since grid handles spacing */
    min-width: 0; /* Allow flex items to shrink below their content size */
    overflow: hidden; /* Prevent content from overflowing the container */
}

/* Remove category headers - we'll show categories as tags instead */
.result-category-header {
    display: none;
}

.result-category-container {
    display: contents; /* This makes the container transparent to the grid */
}

.cipher-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.cipher-name {
    font-weight: bold;
    font-size: 16px;
    flex: 1;
}

.cipher-category-tag {
    background-color: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-left: 10px;
    margin-right: 10px;
}

.cipher-value {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    min-width: 60px;
    text-align: right;
}

.cipher-description {
    color: #666;
    font-size: 12px;
    margin-bottom: 10px;
    line-height: 1.3;
    overflow-x: auto;
    white-space: nowrap;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 6px 8px;
    background-color: #fafafa;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
}

.cipher-description::-webkit-scrollbar {
    height: 6px;
}

.cipher-description::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.cipher-description::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.cipher-description::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.word-breakdown {
    margin-top: 10px;
}

.word-breakdown h4 {
    font-size: 14px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.word-item {
    background-color: #e8f4f8;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
}

.word-name {
    font-weight: bold;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.word-value {
    color: #3498db;
    font-weight: bold;
}

.letter-breakdown {
    font-size: 11px;
    color: #666;
    line-height: 1.2;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    #results-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .cipher-result {
        padding: 12px;
    }

    .cipher-name {
        font-size: 14px;
    }

    .cipher-value {
        font-size: 16px;
    }
}

.word-breakdown h4 {
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.word-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-start;
}

.word-item {
    background-color: #e8f4fc;
    padding: 12px 15px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.word-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    padding-bottom: 5px;
}

.word-text {
    font-weight: 500;
}

.word-value {
    font-weight: bold;
    color: #3498db;
}

.letter-breakdown {
    font-family: monospace;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

/* Footer Styles */
footer {
    text-align: center;
    padding: 10px;
    color: #666;
    font-size: 14px;
}

/* Grid Layout for Results */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

/* Hide Calculate Button when Auto-Calculate is enabled */
.hidden {
    display: none;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .main-content {
        flex-wrap: wrap;
    }

    main {
        order: 1;
        width: 100%;
    }

    .left-sidebar {
        order: 2;
        width: calc(50% - 10px);
    }

    .right-sidebar {
        order: 3;
        width: calc(50% - 10px);
    }
}

@media (max-width: 900px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        max-height: none;
    }

    .cipher-filters, #summary-container {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header, main {
        padding: 15px;
    }

    .cipher-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .word-list {
        flex-direction: column;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }
}

/* Add this to your existing CSS */
.search-box {
    margin-bottom: 15px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Navigation Styles */
nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

nav li, .main-nav li {
    margin: 0 15px;
}

nav a, .main-nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

nav a:hover, nav a.active, .main-nav a:hover, .main-nav a.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group textarea {
    min-height: 150px;
}

.form-submit {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.form-submit:hover {
    background-color: #2980b9;
}